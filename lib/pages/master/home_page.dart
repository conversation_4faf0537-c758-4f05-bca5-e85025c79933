import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/master/home_controller.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/widgets/drawers/main_drawer.dart';
import 'package:myrunway/widgets/historical_activities_widget.dart';

class MasterHomePage extends StatelessWidget {
  const MasterHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final MasterHomeController controller = Get.put(MasterHomeController());

    return Scaffold(
      backgroundColor: AppColors.background,
      drawer: const MainDrawer(),
      appBar: AppBar(
        title: const Text('لوحة تحكم المدير العام'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Show notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshDashboard,
          ),
        ],
      ),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome Section
                      _buildWelcomeSection(controller),

                      // const SizedBox(height: 24),

                      // // System Overview
                      // _buildSystemOverview(controller),
                      const SizedBox(height: 24),

                      // Quick Actions
                      _buildQuickActions(controller),

                      const SizedBox(height: 24),

                      // Historical Activities
                      Obx(
                        () => HistoricalActivitiesWidget(
                          activities: controller.historicalActivities,
                          isLoading: controller.isLoadingActivities.value,
                          title: 'الأنشطة الأخيرة',
                          onRefresh: controller.loadHistoricalActivities,
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildWelcomeSection(MasterHomeController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.primaryLight],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.admin_panel_settings,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مرحباً، ${controller.authService.currentUser?.firstName ?? 'المدير'}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Text(
                      'المدير العام للنظام',
                      style: TextStyle(fontSize: 16, color: Colors.white70),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Icon(
                Icons.business_center,
                color: Colors.white70,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'إدارة ${controller.totalOffices} مكاتب',
                style: const TextStyle(fontSize: 14, color: Colors.white70),
              ),
              const SizedBox(width: 16),
              const Icon(Icons.people, color: Colors.white70, size: 16),
              const SizedBox(width: 8),
              Text(
                '${controller.totalEmployees} موظف',
                style: const TextStyle(fontSize: 14, color: Colors.white70),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(MasterHomeController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إجراءات سريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
          children: [
            // _buildActionCard(
            //   'إدارة المكاتب',
            //   Icons.business,
            //   AppColors.primary,
            //   () => controller.navigateToOffices(),
            // ),
            _buildActionCard(
              'إدارة الموظفين',
              Icons.people,
              AppColors.success,
              () => controller.navigateToEmployees(),
            ),
            _buildActionCard(
              'إدارة الطلبات',
              Icons.assignment,
              AppColors.info,
              () => controller.navigateToOrders(),
            ),
            _buildActionCard(
              'إدارة العملاء',
              Icons.business_center,
              AppColors.warning,
              () => controller.navigateToClients(),
            ),
            // _buildActionCard(
            //   'التقارير المتقدمة',
            //   Icons.analytics,
            //   AppColors.error,
            //   () => controller.navigateToReports(),
            // ),
            // _buildActionCard(
            //   'إعدادات النظام',
            //   Icons.settings,
            //   AppColors.grey600,
            //   () => controller.navigateToSystemSettings(),
            // ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
