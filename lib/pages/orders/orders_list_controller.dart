import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/services/user_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class OrdersListController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final AuthService _authService = Get.find<AuthService>();
  final UserService _userService = Get.find<UserService>();

  // Reactive variables
  final RxList<OrderModelNew> _orders = <OrderModelNew>[].obs;
  final RxList<OrderModelNew> _myOrders = <OrderModelNew>[].obs;
  final RxBool _isLoading = false.obs;
  final Rx<OrderModelNew?> _selectedOrder = Rx<OrderModelNew?>(null);

  // Filter variables
  final Rx<DateTime?> _dateFrom = Rx<DateTime?>(null);
  final Rx<DateTime?> _dateTo = Rx<DateTime?>(null);
  final Rx<OrderHandlingStatus?> _selectedStatus = Rx<OrderHandlingStatus?>(
    null,
  );
  final Rx<UserModel?> _selectedEmployee = Rx<UserModel?>(null);
  final RxList<UserModel> _employees = <UserModel>[].obs;
  final RxBool _filtersExpanded = false.obs;

  // Getters
  List<OrderModelNew> get orders => _orders;
  List<OrderModelNew> get myOrders => _myOrders;
  bool get isLoading => _isLoading.value;
  OrderModelNew? get selectedOrder => _selectedOrder.value;

  // Filter getters
  DateTime? get dateFrom => _dateFrom.value;
  DateTime? get dateTo => _dateTo.value;
  OrderHandlingStatus? get selectedStatus => _selectedStatus.value;
  UserModel? get selectedEmployee => _selectedEmployee.value;
  List<UserModel> get employees => _employees;
  bool get filtersExpanded => _filtersExpanded.value;
  bool get hasActiveFilters =>
      _dateFrom.value != null ||
      _dateTo.value != null ||
      _selectedStatus.value != null ||
      _selectedEmployee.value != null;

  // Permission getters
  bool get canViewAllOrders => _authService.hasPermission(UserRole.manager);
  bool get canCreateOrders => _authService.hasPermission(UserRole.employee);
  bool get canEditOrders => _authService.hasPermission(UserRole.manager);
  bool get canDeleteOrders => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    if (canViewAllOrders) {
      loadEmployees();
      loadOrders();
    } else {
      loadMyOrders();
    }
  }

  // Load employees for assignment filter
  Future<void> loadEmployees() async {
    if (!canViewAllOrders) return;

    final response = await _userService.getEmployees();
    if (response.success && response.data != null) {
      _employees.value = response.data!;
    }
  }

  // Load all orders (for managers and masters)
  Future<void> loadOrders({
    String? dateFrom,
    String? dateTo,
    String? status,
    int? assignedTo,
  }) async {
    if (!canViewAllOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لعرض جميع الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isLoading.value = true;

    // Use current filter values if no parameters provided
    final response = await _orderService.getOrders(
      dateFrom: dateFrom ?? _dateFrom.value?.toIso8601String().split('T')[0],
      dateTo: dateTo ?? _dateTo.value?.toIso8601String().split('T')[0],
      status: status ?? _selectedStatus.value?.name,
      assignedTo: assignedTo ?? _selectedEmployee.value?.id,
    );

    if (response.success && response.data != null) {
      _orders.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Load my orders (for employees)
  Future<void> loadMyOrders({
    String? dateFrom,
    String? dateTo,
    String? status,
  }) async {
    _isLoading.value = true;

    // Use current filter values if no parameters provided
    final response = await _orderService.getMyOrders(
      dateFrom: dateFrom ?? _dateFrom.value?.toIso8601String().split('T')[0],
      dateTo: dateTo ?? _dateTo.value?.toIso8601String().split('T')[0],
      status: status ?? _selectedStatus.value?.name,
    );

    if (response.success && response.data != null) {
      _myOrders.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب طلباتي',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Delete an order
  Future<bool> deleteOrder(int orderId) async {
    if (!canDeleteOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    final response = await _orderService.deleteOrder(orderId);

    if (response.success) {
      _orders.removeWhere((order) => order.id == orderId);
      _myOrders.removeWhere((order) => order.id == orderId);
      Get.snackbar(
        'نجح',
        'تم حذف الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في حذف الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Select an order
  void selectOrder(OrderModelNew order) {
    _selectedOrder.value = order;
  }

  // Clear selection
  void clearSelection() {
    _selectedOrder.value = null;
  }

  // Refresh orders
  Future<void> refreshOrders() async {
    if (canViewAllOrders) {
      await loadOrders();
    } else {
      await loadMyOrders();
    }
  }

  // Filter methods
  void toggleFiltersExpanded() {
    _filtersExpanded.value = !_filtersExpanded.value;
  }

  void setDateFilter(DateTime? from, DateTime? to) {
    _dateFrom.value = from;
    _dateTo.value = to;
    applyFilters();
  }

  void setStatusFilter(OrderHandlingStatus? status) {
    _selectedStatus.value = status;
    applyFilters();
  }

  void setEmployeeFilter(UserModel? employee) {
    _selectedEmployee.value = employee;
    applyFilters();
  }

  void clearFilters() {
    _dateFrom.value = null;
    _dateTo.value = null;
    _selectedStatus.value = null;
    _selectedEmployee.value = null;
    applyFilters();
  }

  void applyFilters() {
    if (canViewAllOrders) {
      loadOrders();
    } else {
      loadMyOrders();
    }
  }

  // Quick date filter methods
  void setTodayFilter() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);
    setDateFilter(startOfDay, endOfDay);
  }

  void setYesterdayFilter() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final startOfDay = DateTime(yesterday.year, yesterday.month, yesterday.day);
    final endOfDay = DateTime(
      yesterday.year,
      yesterday.month,
      yesterday.day,
      23,
      59,
      59,
    );
    setDateFilter(startOfDay, endOfDay);
  }

  void setTomorrowFilter() {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final startOfDay = DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
    final endOfDay = DateTime(
      tomorrow.year,
      tomorrow.month,
      tomorrow.day,
      23,
      59,
      59,
    );
    setDateFilter(startOfDay, endOfDay);
  }

  void setThisWeekFilter() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    final startOfDay = DateTime(
      startOfWeek.year,
      startOfWeek.month,
      startOfWeek.day,
    );
    final endOfDay = DateTime(
      endOfWeek.year,
      endOfWeek.month,
      endOfWeek.day,
      23,
      59,
      59,
    );
    setDateFilter(startOfDay, endOfDay);
  }

  void setLastWeekFilter() {
    final now = DateTime.now();
    final startOfThisWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfLastWeek = startOfThisWeek.subtract(const Duration(days: 7));
    final endOfLastWeek = startOfLastWeek.add(const Duration(days: 6));
    final startOfDay = DateTime(
      startOfLastWeek.year,
      startOfLastWeek.month,
      startOfLastWeek.day,
    );
    final endOfDay = DateTime(
      endOfLastWeek.year,
      endOfLastWeek.month,
      endOfLastWeek.day,
      23,
      59,
      59,
    );
    setDateFilter(startOfDay, endOfDay);
  }

  void setThisMonthFilter() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
    setDateFilter(startOfMonth, endOfMonth);
  }

  void setLastMonthFilter() {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0, 23, 59, 59);
    setDateFilter(startOfLastMonth, endOfLastMonth);
  }
}
