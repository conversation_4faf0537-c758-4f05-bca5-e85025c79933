class HistoricalActivityResponseModel {
  final List<HistoricalActivityModel> activities;
  final int totalCount;
  final int page;
  final int pageSize;
  final bool hasMore;

  HistoricalActivityResponseModel({
    required this.activities,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.hasMore,
  });

  factory HistoricalActivityResponseModel.fromJson(Map<String, dynamic> json) {
    return HistoricalActivityResponseModel(
      activities: (json['activities'] as List<dynamic>)
          .map((activity) => HistoricalActivityModel.fromJson(activity as Map<String, dynamic>))
          .toList(),
      totalCount: json['total_count'] ?? 0,
      page: json['page'] ?? 1,
      pageSize: json['page_size'] ?? 20,
      hasMore: json['has_more'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'activities': activities.map((activity) => activity.toJson()).toList(),
      'total_count': totalCount,
      'page': page,
      'page_size': pageSize,
      'has_more': hasMore,
    };
  }
}

class HistoricalActivityModel {
  final String id;
  final DateTime timestamp;
  final String activityType;
  final String userName;
  final int userId;
  final String description;
  final int? orderId;
  final String? orderCode;
  final String? customerName;
  final Map<String, dynamic>? details;

  HistoricalActivityModel({
    required this.id,
    required this.timestamp,
    required this.activityType,
    required this.userName,
    required this.userId,
    required this.description,
    this.orderId,
    this.orderCode,
    this.customerName,
    this.details,
  });

  factory HistoricalActivityModel.fromJson(Map<String, dynamic> json) {
    return HistoricalActivityModel(
      id: json['id'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      activityType: json['activity_type'] ?? '',
      userName: json['user_name'] ?? '',
      userId: json['user_id'] ?? 0,
      description: json['description'] ?? '',
      orderId: json['order_id'],
      orderCode: json['order_code'],
      customerName: json['customer_name'],
      details: json['details'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'activity_type': activityType,
      'user_name': userName,
      'user_id': userId,
      'description': description,
      'order_id': orderId,
      'order_code': orderCode,
      'customer_name': customerName,
      'details': details,
    };
  }
}
