import 'package:flutter/material.dart';
import 'package:myrunway/core/models/office_performance_models.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/widgets/cards/info_card.dart';

class OfficePerformanceWidget extends StatelessWidget {
  final OfficePerformanceModel? performanceData;
  final bool isLoading;
  final String title;
  final VoidCallback? onRefresh;

  const OfficePerformanceWidget({
    super.key,
    this.performanceData,
    this.isLoading = false,
    this.title = 'أداء المكتب',
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: ListView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onRefresh != null)
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: isLoading ? null : onRefresh,
                  ),
              ],
            ),
            const SizedBox(height: 16),

            if (isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (performanceData == null)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text(
                    'لا توجد بيانات أداء متاحة',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              _buildPerformanceContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceContent() {
    return Column(
      children: [
        // Key Metrics Row
        _buildKeyMetricsRow(),
        const SizedBox(height: 16),

        // Performance Indicators Row
        _buildPerformanceIndicatorsRow(),
        const SizedBox(height: 16),

        // Top Performers Section
        if (performanceData!.topPerformingEmployees.isNotEmpty)
          _buildTopPerformersSection(),
      ],
    );
  }

  Widget _buildKeyMetricsRow() {
    return Row(
      children: [
        Expanded(
          child: StatCard(
            title: 'إجمالي الطلبات',
            value: '${performanceData!.totalOrders}',
            icon: Icons.shopping_bag,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: StatCard(
            title: 'إجمالي الإيرادات',
            value: _formatCurrency(performanceData!.totalRevenue),
            icon: Icons.monetization_on,
            color: AppColors.success,
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceIndicatorsRow() {
    return Row(
      children: [
        Expanded(
          child: StatCard(
            title: 'معدل التحصيل',
            value:
                '${(performanceData!.collectionRate * 100).toStringAsFixed(1)}%',
            icon: Icons.account_balance_wallet,
            color: AppColors.info,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: StatCard(
            title: 'التسليم في الوقت',
            value:
                '${(performanceData!.onTimeDeliveryRate * 100).toStringAsFixed(1)}%',
            icon: Icons.schedule,
            color: AppColors.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildTopPerformersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أفضل الموظفين أداءً',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children:
                performanceData!.topPerformingEmployees
                    .take(3) // Show top 3 performers
                    .map((employee) => _buildEmployeePerformanceItem(employee))
                    .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildEmployeePerformanceItem(EmployeePerformanceModel employee) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              employee.employeeName,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            '${employee.ordersCompleted} طلب',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
          const SizedBox(width: 8),
          Text(
            '${(employee.completionRate * 100).toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCurrency(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}م ر.س';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}ك ر.س';
    } else {
      return '${amount.toStringAsFixed(0)} ر.س';
    }
  }
}

// Additional widget for order status breakdown
class OrderStatusBreakdownWidget extends StatelessWidget {
  final Map<String, int> ordersByStatus;
  final String title;

  const OrderStatusBreakdownWidget({
    super.key,
    required this.ordersByStatus,
    this.title = 'توزيع الطلبات حسب الحالة',
  });

  @override
  Widget build(BuildContext context) {
    if (ordersByStatus.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...ordersByStatus.entries.map(
              (entry) => _buildStatusItem(entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String status, int count) {
    final color = _getStatusColor(status);
    final total = ordersByStatus.values.fold(0, (sum, value) => sum + value);
    final percentage = total > 0 ? (count / total * 100) : 0.0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _getStatusDisplayName(status),
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Text(
            '$count (${percentage.toStringAsFixed(1)}%)',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'في الانتظار';
      case 'processing':
        return 'قيد المعالجة';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }
}
